# ==================================================================================
# ==================================================================================
#                                Deriv CODEOWNERS
# ==================================================================================
# ==================================================================================
#
#  Configuration of code ownership and review approvals for the deriv-com/bot repo.
#
#  More info: https://help.github.com/articles/about-codeowners/
#


# ================================================
#  General rules / philosophy 
# ================================================
#
#  - we trust that people do the right thing and not approve changes they don't feel confident reviewing
#  - we use github teams so that we funnel code reviews to the most appropriate reviewer, this is why the team structure is fine-grained
#  - we enforce that only approved PRs get merged to ensure that unreviewed code doesn't get accidentally merged
#  - we delegate approval rights as much as possible so that we can scale better
#  - each group must have at least one person, but several people are preferable to avoid a single point of failure issues
#
#  Configuration nuances:
#
#  - This configuration works in conjunction with the protected branch settings that require all changes to be made via pull requests with at least one approval.
#  - This approval can come from an appropriate codeowner, or any repo collaborator (person with write access) if the PR is authored by a codeowner.
#  - Each codeowners team must have write access to the repo, otherwise their reviews won't count.
#
#  In the case of emergency, the repo administrators can bypass this requirement.



# ================================================
#  GitHub username registry
#  (just to make this file easier to understand)
# ================================================

#  ali-hosseini-deriv
#  yashim-deriv
#  wojciech-deriv
#  markwylde-deriv
#  sandeep-deriv
#  shafin-deriv

######################################################################################################
#
# CODEOWNERS rules
# -----------------
#
# All the following rules are applied in the order specified in this file.
# The last rule that matches wins!
#
# See https://git-scm.com/docs/gitignore#_pattern_format for pattern syntax docs.
#
######################################################################################################


# ==============================================================
#  Default Owners
# (in case no pattern matches a path in a PR - this should be treated as a bug and result in adding the path to CODEOWNERS)
# ==============================================================

*  @ali-hosseini-deriv @yashim-deriv @wojciech-deriv @markwylde-deriv
*  @sandeep-deriv @shafin-deriv