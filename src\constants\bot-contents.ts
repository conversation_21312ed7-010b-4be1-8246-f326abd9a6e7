type TTabsTitle = {
    [key: string]: string | number;
};

type TDashboardTabIndex = {
    [key: string]: number;
};

export const tabs_title: TTabsTitle = Object.freeze({
    WORKSPACE: 'Workspace',
    CHART: 'Chart',
});

export const DBOT_TABS: TDashboardTabIndex = Object.freeze({
    DASHBOARD: 0,
    BOT_BUILDER: 1,
    FREE_BOTS: 2,
    ANALYSIS_TOOLS: 3,
    SMART_TRADER: 4,
    SIGNAL_ZONE: 5,
    CHART: 6,
    COPY_TRADING: 7,
});

export const MAX_STRATEGIES = 10;

export const TAB_IDS = [
    'id-dbot-dashboard',
    'id-bot-builder',
    'id-free-bots',
    'id-analysis-tools',
    'id-smart-trader',
    'id-signal-zone',
    'id-charts',
    'id-copy-trading'
];

export const DEBOUNCE_INTERVAL_TIME = 500;
