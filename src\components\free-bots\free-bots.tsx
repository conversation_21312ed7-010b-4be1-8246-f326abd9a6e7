import React from 'react';
import { observer } from 'mobx-react-lite';
import { useStore } from '@/hooks/useStore';
import { DBOT_TABS } from '@/constants/bot-contents';
import { localize } from '@deriv-com/translations';
import './free-bots.scss';

interface BotCard {
    id: number;
    name: string;
    description: string;
    xmlFile?: string; // Will be added later
}

const FreeBots: React.FC = observer(() => {
    const { dashboard } = useStore();
    const { setActiveTab } = dashboard;

    // Placeholder bot data - 10 cards as requested
    const botCards: BotCard[] = [
        { id: 1, name: 'Bot 1', description: 'Placeholder bot description' },
        { id: 2, name: 'Bot 2', description: 'Placeholder bot description' },
        { id: 3, name: 'Bot 3', description: 'Placeholder bot description' },
        { id: 4, name: 'Bot 4', description: 'Placeholder bot description' },
        { id: 5, name: 'Bot 5', description: 'Placeholder bot description' },
        { id: 6, name: 'Bot 6', description: 'Placeholder bot description' },
        { id: 7, name: 'Bot 7', description: 'Placeholder bot description' },
        { id: 8, name: 'Bot 8', description: 'Placeholder bot description' },
        { id: 9, name: 'Bot 9', description: 'Placeholder bot description' },
        { id: 10, name: 'Bot 10', description: 'Placeholder bot description' },
    ];

    const handleBotClick = (bot: BotCard) => {
        // TODO: Load XML file to bot builder when XML files are added
        console.log(`Loading bot: ${bot.name}`);
        
        // Navigate to Bot Builder tab
        setActiveTab(DBOT_TABS.BOT_BUILDER);
        
        // TODO: Load the specific bot's XML file into the workspace
        // This will be implemented when XML files are provided
    };

    return (
        <div className="free-bots-container">
            <div className="free-bots-header">
                <h2>{localize('Free Bots')}</h2>
                <p>{localize('Click on any bot to automatically load it into the Bot Builder')}</p>
            </div>
            
            <div className="free-bots-grid">
                {botCards.map((bot) => (
                    <div
                        key={bot.id}
                        className="bot-card"
                        onClick={() => handleBotClick(bot)}
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                                handleBotClick(bot);
                            }
                        }}
                    >
                        <div className="bot-card-content">
                            <div className="bot-card-icon">
                                <svg
                                    width="40"
                                    height="40"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M12 2L2 7L12 12L22 7L12 2Z"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                    <path
                                        d="M2 17L12 22L22 17"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                    <path
                                        d="M2 12L12 17L22 12"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                            </div>
                            <div className="bot-card-info">
                                <h3 className="bot-card-title">{bot.name}</h3>
                                <p className="bot-card-description">{bot.description}</p>
                            </div>
                        </div>
                        <div className="bot-card-overlay">
                            <span className="bot-card-action">{localize('Click to Load')}</span>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
});

export default FreeBots;
