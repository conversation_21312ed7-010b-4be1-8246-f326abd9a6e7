name: Sync translations

on:
  push:
    branches:
      - master
  schedule:
    - cron: '0 */12 * * *'
  workflow_dispatch:
        
jobs:
  sync_translations:
    runs-on: ubuntu-latest
    environment: staging
    permissions:
      contents: read
    steps:
      - name: Sync Bots translations
        uses: deriv-com/translations/.github/actions/extract_and_sync_translations@master
        with:
          PROJECT_NAME: ${{ vars.R2_PROJECT_NAME }}
          CROWDIN_BRANCH_NAME: staging
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
          R2_ACCOUNT_ID: ${{ secrets.R2_ACCOUNT_ID }}
          R2_ACCESS_KEY_ID: ${{ secrets.R2_ACCESS_KEY_ID }}
          R2_SECRET_ACCESS_KEY: ${{ secrets.R2_SECRET_ACCESS_KEY }}
          R2_BUCKET_NAME: ${{ secrets.R2_BUCKET_NAME }}
          PROJECT_SOURCE_DIRECTORY: "src"
  sync_translations_production:
    runs-on: ubuntu-latest
    environment: production
    permissions:
      contents: read
    steps:
      - name: Sync Bots translations
        uses: deriv-com/translations/.github/actions/extract_and_sync_translations@master
        with:
          PROJECT_NAME: ${{ vars.R2_PROJECT_NAME }}
          CROWDIN_BRANCH_NAME: production
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
          R2_ACCOUNT_ID: ${{ secrets.R2_ACCOUNT_ID }}
          R2_ACCESS_KEY_ID: ${{ secrets.R2_ACCESS_KEY_ID }}
          R2_SECRET_ACCESS_KEY: ${{ secrets.R2_SECRET_ACCESS_KEY }}
          R2_BUCKET_NAME: ${{ secrets.R2_BUCKET_NAME }}
          PROJECT_SOURCE_DIRECTORY: "src"
