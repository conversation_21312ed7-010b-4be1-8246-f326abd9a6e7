import React, { useEffect, useRef, useState } from "react";
import { Localize } from '@deriv-com/translations';
import './copy-trading.scss';

interface CopyTradingProps {}

const CopyTrading: React.FC<CopyTradingProps> = () => {
  const [demoToken, setDemoToken] = useState("");
  const [clientTokens, setClientTokens] = useState<string[]>([]);
  const [accountToCopy, setAccountToCopy] = useState("");
  const [targetAccount, setTargetAccount] = useState("");
  const [log, setLog] = useState<string[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const demoWS = useRef<WebSocket | null>(null);
  const clientWS = useRef<{[key: string]: WebSocket}>({});

  const logMessage = (msg: string) => setLog((prev) => [msg, ...prev]);

  const connectDemoWS = () => {
    if (!demoToken) return alert("Enter demo token");

    demoWS.current = new WebSocket("wss://ws.derivws.com/websockets/v3/websocket");
    demoWS.current.onopen = () => {
      if (demoWS.current) {
        demoWS.current.send(
          JSON.stringify({ authorize: demoToken })
        );
        setIsConnected(true);
        logMessage("Demo account connected.");
      }
    };

    demoWS.current.onmessage = (msg) => {
      const data = JSON.parse(msg.data);
      if (data.msg_type === "authorize") {
        if (demoWS.current) {
          demoWS.current.send(
            JSON.stringify({
              subscribe: 1,
              proposal: 1,
              amount: 1,
              basis: "stake",
              contract_type: "CALL",
              currency: "USD",
              duration: 1,
              duration_unit: "t",
              symbol: "R_100"
            })
          );
          logMessage("Subscribed to demo trade proposals.");
        }
      }

      if (data.msg_type === "proposal") {
        replicateTradeToClients(data);
      }
    };
  };

  const replicateTradeToClients = (tradeData: any) => {
    clientTokens.forEach((token) => {
      if (!clientWS.current[token]) {
        clientWS.current[token] = new WebSocket("wss://ws.derivws.com/websockets/v3/websocket");

        clientWS.current[token].onopen = () => {
          clientWS.current[token].send(JSON.stringify({ authorize: token }));
          logMessage(`Connected to real client with token: ${token.slice(0, 4)}...`);
        };
      }

      clientWS.current[token].onmessage = (msg) => {
        const data = JSON.parse(msg.data);
        if (data.msg_type === "authorize") {
          // Send trade request
          clientWS.current[token].send(
            JSON.stringify({
              buy: tradeData.proposal.id,
              price: 1,
              parameters: tradeData.proposal
            })
          );
          logMessage(`Trade replicated to: ${token.slice(0, 4)}...`);
        }
      };
    });
  };

  const addClientToken = () => {
    if (clientTokens.includes(accountToCopy) || !accountToCopy) return;
    setClientTokens([...clientTokens, accountToCopy]);
    logMessage(`Added target client: ${accountToCopy.slice(0, 4)}...`);
    setAccountToCopy("");
  };

  const disconnectDemo = () => {
    if (demoWS.current) {
      demoWS.current.close();
      setIsConnected(false);
      logMessage("Demo account disconnected.");
    }
  };

  const removeClient = (tokenToRemove: string) => {
    setClientTokens(clientTokens.filter(token => token !== tokenToRemove));
    if (clientWS.current[tokenToRemove]) {
      clientWS.current[tokenToRemove].close();
      delete clientWS.current[tokenToRemove];
    }
    logMessage(`Removed client: ${tokenToRemove.slice(0, 4)}...`);
  };

  return (
    <div className="deriv-copy-trading">
      <div className="copy-trading-header">
        <h1><Localize i18n_default_text="Deriv Copy Trading" /></h1>
        <p><Localize i18n_default_text="Facilitate demo to real account copy trading and use valid tokens to copy trades to others" /></p>
      </div>

      <div className="trading-grid">
        {/* Demo Account Section */}
        <div className="trading-card demo-card">
          <div className="card-header">
            <h3><Localize i18n_default_text="Demo Account" /></h3>
            <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
              <div className="status-dot"></div>
              <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
            </div>
          </div>

          <div className="card-content">
            <div className="input-group">
              <label><Localize i18n_default_text="Demo Token" /></label>
              <input
                type="text"
                value={demoToken}
                onChange={(e) => setDemoToken(e.target.value)}
                placeholder="Enter demo account token"
                className="token-input"
              />
            </div>

            <div className="button-group">
              <button
                onClick={connectDemoWS}
                disabled={isConnected || !demoToken}
                className="btn btn-primary"
              >
                <Localize i18n_default_text="Connect & Start Copy" />
              </button>
              <button
                onClick={disconnectDemo}
                disabled={!isConnected}
                className="btn btn-secondary"
              >
                <Localize i18n_default_text="Disconnect" />
              </button>
            </div>
          </div>
        </div>

        {/* Client Management Section */}
        <div className="trading-card clients-card">
          <div className="card-header">
            <h3><Localize i18n_default_text="Client Management" /></h3>
            <div className="client-count">
              <span className="count">{clientTokens.length}</span>
              <span className="label">Clients</span>
            </div>
          </div>

          <div className="card-content">
            <div className="input-group">
              <label><Localize i18n_default_text="Add Client Token (for replication)" /></label>
              <div className="input-with-button">
                <input
                  type="text"
                  value={accountToCopy}
                  onChange={(e) => setAccountToCopy(e.target.value)}
                  placeholder="Enter real account token"
                  className="token-input"
                />
                <button
                  onClick={addClientToken}
                  disabled={!accountToCopy}
                  className="btn btn-add"
                >
                  <Localize i18n_default_text="Add" />
                </button>
              </div>
            </div>

            <div className="clients-list">
              <h4><Localize i18n_default_text="Connected Clients:" /></h4>
              {clientTokens.length === 0 ? (
                <div className="no-clients">
                  <Localize i18n_default_text="No clients connected yet" />
                </div>
              ) : (
                <ul className="client-items">
                  {clientTokens.map((token, idx) => (
                    <li key={idx} className="client-item">
                      <div className="client-info">
                        <span className="client-icon">✔️</span>
                        <span className="client-token">{token.slice(0, 6)}...</span>
                      </div>
                      <button
                        onClick={() => removeClient(token)}
                        className="btn btn-remove"
                      >
                        ×
                      </button>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Live Logs Section */}
      <div className="logs-section">
        <div className="logs-header">
          <h3><Localize i18n_default_text="Live Trading Logs" /></h3>
          <button
            onClick={() => setLog([])}
            className="btn btn-clear"
          >
            <Localize i18n_default_text="Clear Logs" />
          </button>
        </div>
        <div className="logs-container">
          {log.length === 0 ? (
            <div className="no-logs">
              <Localize i18n_default_text="No activity yet. Connect your demo account to start logging." />
            </div>
          ) : (
            log.map((line, idx) => (
              <div key={idx} className="log-line">
                <span className="log-time">{new Date().toLocaleTimeString()}</span>
                <span className="log-message">{line}</span>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default CopyTrading;
