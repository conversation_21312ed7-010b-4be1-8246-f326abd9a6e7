.deriv-copy-trading {
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    box-sizing: border-box;
    overflow-y: auto;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

    .copy-trading-header {
        text-align: center;
        margin-bottom: 30px;
        color: white;

        h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        p {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
            max-width: 600px;
            margin: 0 auto;
        }
    }

    .trading-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
        }
    }

    .trading-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: transform 0.3s ease, box-shadow 0.3s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid #f0f0f0;

            h3 {
                font-size: 20px;
                font-weight: 600;
                color: #333;
                margin: 0;
            }
        }

        .card-content {
            .input-group {
                margin-bottom: 20px;

                label {
                    display: block;
                    font-size: 14px;
                    font-weight: 500;
                    color: #555;
                    margin-bottom: 8px;
                }

                .token-input {
                    width: 100%;
                    padding: 12px 16px;
                    border: 2px solid #e1e5e9;
                    border-radius: 8px;
                    font-size: 14px;
                    transition: all 0.3s ease;
                    box-sizing: border-box;

                    &:focus {
                        outline: none;
                        border-color: #667eea;
                        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                    }

                    &::placeholder {
                        color: #999;
                    }
                }

                .input-with-button {
                    display: flex;
                    gap: 8px;

                    .token-input {
                        flex: 1;
                    }
                }
            }
        }
    }

    .connection-status {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        &.connected {
            color: #22c55e;

            .status-dot {
                background: #22c55e;
                box-shadow: 0 0 8px rgba(34, 197, 94, 0.4);
                animation: pulse 2s infinite;
            }
        }

        &.disconnected {
            color: #ef4444;

            .status-dot {
                background: #ef4444;
            }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    }

    .client-count {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        .count {
            font-size: 24px;
            font-weight: 700;
            color: #667eea;
            line-height: 1;
        }

        .label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }

    .button-group {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
    }

    .btn {
        padding: 12px 20px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        &:hover::before {
            left: 100%;
        }

        &:disabled {
            opacity: 0.6;
            cursor: not-allowed;

            &::before {
                display: none;
            }
        }

        &.btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

            &:hover:not(:disabled) {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            }
        }

        &.btn-secondary {
            background: #6b7280;
            color: white;

            &:hover:not(:disabled) {
                background: #4b5563;
                transform: translateY(-2px);
            }
        }

        &.btn-add {
            background: #10b981;
            color: white;
            min-width: 80px;

            &:hover:not(:disabled) {
                background: #059669;
                transform: translateY(-2px);
            }
        }

        &.btn-remove {
            background: #ef4444;
            color: white;
            width: 32px;
            height: 32px;
            padding: 0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            line-height: 1;

            &:hover:not(:disabled) {
                background: #dc2626;
                transform: scale(1.1);
            }
        }

        &.btn-clear {
            background: #f59e0b;
            color: white;
            font-size: 12px;
            padding: 8px 16px;

            &:hover:not(:disabled) {
                background: #d97706;
            }
        }
    }

    .clients-list {
        h4 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .no-clients {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px dashed #ddd;
        }

        .client-items {
            list-style: none;
            padding: 0;
            margin: 0;
            max-height: 200px;
            overflow-y: auto;

            .client-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;
                background: #f8f9fa;
                border-radius: 8px;
                margin-bottom: 8px;
                border: 1px solid #e9ecef;
                transition: all 0.3s ease;

                &:hover {
                    background: #e9ecef;
                    transform: translateX(4px);
                }

                .client-info {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .client-icon {
                        font-size: 16px;
                    }

                    .client-token {
                        font-family: 'Monaco', 'Menlo', monospace;
                        font-size: 14px;
                        color: #333;
                        font-weight: 500;
                    }
                }
            }
        }
    }

    .logs-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);

        .logs-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 2px solid #f0f0f0;

            h3 {
                font-size: 20px;
                font-weight: 600;
                color: #333;
                margin: 0;
            }
        }

        .logs-container {
            background: #1a1a1a;
            border-radius: 12px;
            padding: 16px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.4;

            &::-webkit-scrollbar {
                width: 8px;
            }

            &::-webkit-scrollbar-track {
                background: #2a2a2a;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-thumb {
                background: #555;
                border-radius: 4px;

                &:hover {
                    background: #777;
                }
            }

            .no-logs {
                color: #888;
                text-align: center;
                padding: 40px 20px;
                font-style: italic;
            }

            .log-line {
                display: flex;
                gap: 12px;
                margin-bottom: 8px;
                padding: 4px 0;
                border-bottom: 1px solid #2a2a2a;

                &:last-child {
                    border-bottom: none;
                }

                .log-time {
                    color: #10b981;
                    font-weight: 500;
                    min-width: 80px;
                    flex-shrink: 0;
                }

                .log-message {
                    color: #e5e7eb;
                    flex: 1;
                }
            }
        }
    }

    // Responsive design
    @media (max-width: 768px) {
        padding: 16px;

        .copy-trading-header {
            margin-bottom: 20px;

            h1 {
                font-size: 24px;
            }

            p {
                font-size: 14px;
            }
        }

        .trading-card {
            padding: 16px;

            .card-header h3 {
                font-size: 18px;
            }
        }

        .logs-section {
            padding: 16px;

            .logs-container {
                height: 200px;
                font-size: 12px;
            }
        }

        .btn {
            padding: 10px 16px;
            font-size: 13px;
        }
    }
}
