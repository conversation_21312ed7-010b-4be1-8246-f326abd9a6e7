.free-bots-container {
    padding: 2rem;
    background: #0f0f23; /* Dark background to match your screenshot */
    min-height: 100vh;
    width: 100%;
    height: 100%;
    overflow-y: auto;

    .free-bots-header {
        margin-bottom: 2rem;
        text-align: left;

        h2 {
            font-size: 2.4rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 0.8rem;
        }

        p {
            font-size: 1.4rem;
            color: rgba(255, 255, 255, 0.7);
            opacity: 0.8;
        }
    }

    .free-bots-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
        width: 100%;
        max-width: none;

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            grid-template-columns: repeat(2, 1fr);
        }

        @media (min-width: 1025px) {
            grid-template-columns: repeat(3, 1fr);
        }

        @media (min-width: 1400px) {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    .bot-card {
        background: #1e1e5e; /* Match navigation bar color */
        border-radius: 8px;
        padding: 0.8rem;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        border: 2px solid transparent;
        min-height: 60px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        aspect-ratio: 5/1; /* Much wider and shorter cards */

        &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(30, 30, 94, 0.3);
            border-color: #ff6b35; /* Orange border on hover */

            .bot-card-overlay {
                opacity: 1;
            }

            .bot-card-icon svg {
                transform: scale(1.1);
            }
        }

        &:focus {
            outline: none;
            border-color: #ff6b35;
            box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.2);
        }

        &:active {
            transform: translateY(-2px);
        }

        .bot-card-content {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            position: relative;
            z-index: 2;
            height: 100%;
        }

        .bot-card-icon {
            flex-shrink: 0;
            width: 28px;
            height: 28px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;

            svg {
                transition: transform 0.3s ease;
                width: 16px;
                height: 16px;
            }
        }

        .bot-card-info {
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .bot-card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 0.2rem;
            line-height: 1.2;
        }

        .bot-card-description {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.2;
            margin: 0;
        }

        .bot-card-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.9), rgba(255, 107, 53, 0.7));
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 3;

            .bot-card-action {
                font-size: 1.4rem;
                font-weight: 600;
                color: #ffffff;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
        }

        // Add subtle animation
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
            z-index: 1;
        }

        &:hover::before {
            left: 100%;
        }
    }

    // Loading state for future XML integration
    .bot-card--loading {
        pointer-events: none;
        opacity: 0.6;

        .bot-card-icon {
            animation: pulse 1.5s ease-in-out infinite;
        }
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.5;
        }
    }

    // Responsive adjustments
    @media (max-width: 768px) {
        padding: 1rem;

        .free-bots-header {
            margin-bottom: 1.5rem;

            h2 {
                font-size: 2rem;
            }

            p {
                font-size: 1.3rem;
            }
        }

        .bot-card {
            padding: 0.6rem;
            min-height: 50px;
            aspect-ratio: 6/1;

            .bot-card-content {
                gap: 0.6rem;
            }

            .bot-card-icon {
                width: 24px;
                height: 24px;

                svg {
                    width: 14px;
                    height: 14px;
                }
            }

            .bot-card-title {
                font-size: 1rem;
                margin-bottom: 0.1rem;
            }

            .bot-card-description {
                font-size: 0.8rem;
            }
        }
    }

    @media (min-width: 1600px) {
        .free-bots-grid {
            grid-template-columns: repeat(5, 1fr);
        }
    }
}
