/** @define data-list; weak */

@use 'components/shared/styles/devices' as *;
@use 'components/shared/styles/constants' as *;

.data-list {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;

    &__body {
        flex: 1;

        &-wrapper {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
    }

    &__footer {
        width: 100%;
        background: var(--general-main-1);
        border-top: 2px solid var(--border-disabled);
        display: flex;
        align-items: center;
        position: relative;
    }

    &__item {
        height: inherit;

        &--wrapper {
            height: inherit;
            text-decoration: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }

        @include mobile-or-tablet-screen {
            border-radius: $BORDER_RADIUS;
        }
    }

    &__row {
        display: flex;
        flex-direction: row;
        padding: 4px 16px;
        width: 100%;

        > * {
            flex: 1;
        }

        &-content {
            font-size: 1.4rem;
            line-height: 2rem;
            color: var(--text-general);
        }

        &-cell {
            &--amount {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                flex: none;
            }
        }

        &-title {
            font-size: 1.4rem;
            font-weight: bold;
            color: var(--text-prominent);
            line-height: 2rem;

            @include mobile-or-tablet-screen {
                font-size: 1.2rem;
            }
        }

        &-divider {
            margin: 0 1.6rem;

            &:after {
                content: '';
                display: block;
                border-top: 1px solid var(--general-main-1);
            }
        }

        &--wrapper:not(.data-list__item--dynamic-height-wrapper) {
            height: 100%;
        }

        &--timer {
            flex: none;
        }
    }

    &__desc {
        &--wrapper {
            height: inherit;
            display: flex;
            text-align: center;
            align-items: center;
            font-size: var(--text-size-xxs);
            color: var(--text-general);
            padding: 1rem;
        }
    }
}

/* stylelint-disable-next-line plugin/selector-bem-pattern */
.ReactVirtualized__List {
    outline: 0;
}
