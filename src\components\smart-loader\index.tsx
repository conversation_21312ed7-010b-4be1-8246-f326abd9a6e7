import React, { useEffect, useState } from 'react';

interface SmartLoaderProps {
    onLoadingComplete: () => void;
    children: React.ReactNode;
}

const DerivLoaderContent = () => {
  useEffect(() => {
    // Add a delay to ensure DOM is ready
    const initAnimations = () => {
      // Create starfield background
      const cosmos = document.getElementById('cosmos');
      if (cosmos) {
        cosmos.innerHTML = ''; // Clear existing
        for (let i = 0; i < 150; i++) {
          const star = document.createElement('div');
          star.classList.add('star');
          star.style.left = `${Math.random() * 100}%`;
          star.style.top = `${Math.random() * 100}%`;
          star.style.width = `${Math.random() * 3 + 1}px`;
          star.style.height = star.style.width;
          star.style.setProperty('--duration', `${Math.random() * 3 + 2}s`);
          star.style.setProperty('--opacity', `${Math.random() * 0.8 + 0.2}`);
          cosmos.appendChild(star);
        }
      }

      // Create money rain
      const moneyRain = document.getElementById('moneyRain');
      if (moneyRain) {
        moneyRain.innerHTML = ''; // Clear existing
        for (let i = 0; i < 50; i++) {
          const dollar = document.createElement('div');
          dollar.classList.add('dollar-bill');
          dollar.innerHTML = '💵';
          dollar.style.left = `${Math.random() * 100}%`;
          dollar.style.animationDuration = `${Math.random() * 5 + 5}s`;
          dollar.style.animationDelay = `${Math.random() * 5}s`;
          dollar.style.fontSize = `${Math.random() * 20 + 16}px`;
          moneyRain.appendChild(dollar);
        }
      }

      // Create binary code rain
      const binaryAnimation = document.getElementById('binaryAnimation');
      if (binaryAnimation) {
        binaryAnimation.innerHTML = ''; // Clear existing
        for (let i = 0; i < 20; i++) {
          const binary = document.createElement('div');
          binary.classList.add('binary-code');
          binary.style.left = `${Math.random() * 100}%`;
          binary.style.animationDuration = `${Math.random() * 10 + 10}s`;
          binary.style.animationDelay = `${Math.random() * 5}s`;

          // Generate random binary string
          let binaryString = '';
          for (let j = 0; j < 50; j++) {
            binaryString += Math.random() > 0.5 ? '1' : '0';
            if (j % 5 === 4) binaryString += ' ';
          }
          binary.textContent = binaryString;

          binaryAnimation.appendChild(binary);
        }
      }
    };

    // Initialize animations with delay
    setTimeout(initAnimations, 100);

    // Progress animation
    const progressBar = document.getElementById('progressBar');
    const percentage = document.getElementById('percentage');
    const startTime = Date.now();
    const duration = 5000; // 5 seconds

    function updateProgress() {
      const elapsed = Date.now() - startTime;
      const progress = easeInOutCubic(Math.min(elapsed / duration, 1)) * 98 + 2;

      if (progressBar) progressBar.style.width = `${progress}%`;
      if (percentage) percentage.textContent = `${Math.floor(progress)}%`;

      if (elapsed < duration) {
        requestAnimationFrame(updateProgress);
      } else {
        // Loading complete
        if (progressBar) progressBar.style.width = '100%';
        if (percentage) percentage.textContent = '100%';

        // Add completion effects
        const statusDot = document.querySelector('.status-dot') as HTMLElement;
        const statusText = document.querySelector('.status-text') as HTMLElement;
        if (statusDot) statusDot.style.background = '#4ade80';
        if (statusText) statusText.textContent = 'Connection established';

        // Pulsing completion effect
        setTimeout(() => {
          const completionPulse = document.createElement('div');
          completionPulse.style.position = 'absolute';
          completionPulse.style.top = '0';
          completionPulse.style.left = '0';
          completionPulse.style.right = '0';
          completionPulse.style.bottom = '0';
          completionPulse.style.background = 'radial-gradient(circle, rgba(0, 240, 255, 0.3) 0%, transparent 70%)';
          completionPulse.style.borderRadius = '24px';
          completionPulse.style.animation = 'fadeOut 1s forwards';
          const loaderContainer = document.querySelector('.loader-container');
          if (loaderContainer) loaderContainer.appendChild(completionPulse);

          // Add CSS for fadeOut
          const style = document.createElement('style');
          style.textContent = `
            @keyframes fadeOut {
              0% { opacity: 1; transform: scale(1); }
              100% { opacity: 0; transform: scale(1.2); }
            }
          `;
          document.head.appendChild(style);
        }, 300);
      }
    }

    // Easing function
    function easeInOutCubic(t: number) {
      return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    // Start the animation
    requestAnimationFrame(updateProgress);
  }, []);

  return (
    <div className="loader-wrapper">
      {/* Cosmic Background */}
      <div className="cosmos" id="cosmos"></div>

      {/* Money Rain */}
      <div className="money-rain" id="moneyRain"></div>

      {/* Binary Animation */}
      <div className="binary-animation" id="binaryAnimation"></div>

      {/* Main Loader */}
      <div className="loader-container">
        <div className="logo">
          <i className="fas fa-chart-line logo-icon"></i>
          <div className="logo-text">DERIV CONNECT</div>
        </div>

        <div className="powered-by">Powered by Deriv API</div>

        <div className="subtitle">
          Your Trusted and Approved Deriv Third Party Application
          <br />Securely connecting to institutional-grade trading servers
        </div>

        <div className="connection-status">
          <div className="status-dot"></div>
          <div className="status-text">Establishing secure connection...</div>
        </div>

        <div className="progress-container">
          <div className="progress-bar" id="progressBar">
            <div className="progress-fill"></div>
            <div className="progress-glow"></div>
          </div>
        </div>

        <div className="percentage-container">
          <div className="percentage" id="percentage">2%</div>
          <div className="loading-details">Encrypted • 256-bit SSL</div>
        </div>

        <div className="footer">
          <div className="copyright">© 2023 Deriv API. All rights reserved.</div>
          <div className="security-badge">
            <i className="fas fa-lock security-icon"></i>
            <div className="security-text">Secure Connection</div>
          </div>
        </div>
      </div>

      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
        @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

        :root {
          --primary: #00f0ff;
          --secondary: #7b2ff7;
          --accent: #ff2d75;
          --dark: #0a0e17;
          --darker: #050811;
          --light: #e0f7fa;
          --money-green: #00c853;
        }

        .loader-wrapper {
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          background: var(--darker);
          display: flex;
          justify-content: center;
          align-items: center;
          font-family: 'Space Grotesk', sans-serif;
          color: var(--light);
          min-height: 100vh;
          overflow: hidden;
          position: relative;
        }

        /* Cosmic Background */
        .cosmos {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
          overflow: hidden;
          pointer-events: none;
        }

        .star {
          position: absolute;
          background: white;
          border-radius: 50%;
          animation: twinkle var(--duration) infinite ease-in-out;
          opacity: 0;
          box-shadow: 0 0 6px rgba(255, 255, 255, 0.8);
        }

        @keyframes twinkle {
          0%, 100% {
            opacity: 0;
            transform: scale(0.5);
            box-shadow: 0 0 6px rgba(255, 255, 255, 0.8);
          }
          50% {
            opacity: var(--opacity);
            transform: scale(1.2);
            box-shadow: 0 0 12px rgba(255, 255, 255, 1);
          }
        }

        /* Falling Dollar Bills */
        .money-rain {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
          z-index: 1;
        }

        .dollar-bill {
          position: absolute;
          color: var(--money-green);
          font-size: 24px;
          animation: falling linear infinite;
          transform: rotate(20deg);
          text-shadow: 0 0 10px rgba(0, 200, 83, 0.8);
          opacity: 0;
        }

        @keyframes falling {
          0% {
            transform: translateY(-100px) rotate(20deg);
            opacity: 0;
          }
          10% {
            opacity: 0.9;
          }
          90% {
            opacity: 0.9;
          }
          100% {
            transform: translateY(100vh) rotate(60deg);
            opacity: 0;
          }
        }

        /* Main Container */
        .loader-container {
          width: 480px;
          background: rgba(10, 14, 23, 0.8);
          border-radius: 24px;
          padding: 40px;
          box-shadow: 0 0 60px rgba(0, 240, 255, 0.15),
                      0 30px 50px rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(12px);
          border: 1px solid rgba(0, 240, 255, 0.1);
          position: relative;
          overflow: hidden;
          transform: translateY(0);
          animation: float 6s ease-in-out infinite;
          z-index: 10;
        }

        @keyframes float {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-15px); }
        }

        /* Neon Border Effect */
        .loader-container::before {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          background: linear-gradient(45deg,
                      var(--primary),
                      var(--secondary),
                      var(--accent),
                      var(--primary));
          background-size: 400%;
          border-radius: 24px;
          z-index: -1;
          animation: borderGlow 8s linear infinite;
          opacity: 0.7;
          filter: blur(8px);
        }

        @keyframes borderGlow {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }

        /* Header */
        .logo {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 20px;
        }

        .logo-text {
          font-size: 36px;
          font-weight: 700;
          background: linear-gradient(90deg, var(--primary), var(--secondary));
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          letter-spacing: 2px;
          text-shadow: 0 0 20px rgba(0, 240, 255, 0.3);
          position: relative;
        }

        .logo-icon {
          margin-right: 15px;
          font-size: 28px;
          color: var(--primary);
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }

        .powered-by {
          font-size: 14px;
          color: rgba(224, 247, 250, 0.7);
          text-align: center;
          margin-bottom: 10px;
          font-weight: 300;
        }

        .subtitle {
          font-size: 14px;
          color: rgba(224, 247, 250, 0.7);
          text-align: center;
          margin-bottom: 40px;
          line-height: 1.6;
          font-weight: 300;
        }

        /* Connection Indicator */
        .connection-status {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 30px;
        }

        .status-dot {
          width: 12px;
          height: 12px;
          background: var(--accent);
          border-radius: 50%;
          margin-right: 10px;
          position: relative;
        }

        .status-dot::after {
          content: '';
          position: absolute;
          top: -3px;
          left: -3px;
          right: -3px;
          bottom: -3px;
          border: 1px solid var(--accent);
          border-radius: 50%;
          animation: ripple 1.5s infinite;
          opacity: 0;
        }

        @keyframes ripple {
          0% { transform: scale(0.5); opacity: 0.5; }
          100% { transform: scale(2); opacity: 0; }
        }

        .status-text {
          font-size: 16px;
          font-weight: 500;
          color: var(--light);
        }

        /* Progress Bar */
        .progress-container {
          width: 100%;
          height: 10px;
          background: rgba(0, 240, 255, 0.1);
          border-radius: 10px;
          margin-bottom: 15px;
          overflow: hidden;
          position: relative;
          box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
        }

        .progress-bar {
          height: 100%;
          width: 2%;
          border-radius: 10px;
          position: relative;
          transition: width 0.4s cubic-bezier(0.65, 0, 0.35, 1);
        }

        .progress-fill {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg,
                      var(--primary),
                      var(--secondary));
          border-radius: 10px;
          box-shadow: 0 0 20px rgba(0, 240, 255, 0.3);
        }

        .progress-glow {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg,
                      rgba(255, 255, 255, 0) 0%,
                      rgba(255, 255, 255, 0.6) 50%,
                      rgba(255, 255, 255, 0) 100%);
          animation: shine 2s infinite;
          border-radius: 10px;
          opacity: 0.8;
        }

        @keyframes shine {
          0% { transform: translateX(-200%); }
          100% { transform: translateX(200%); }
        }

        /* Percentage */
        .percentage-container {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
        }

        .percentage {
          font-size: 24px;
          font-weight: 700;
          background: linear-gradient(90deg, var(--primary), var(--secondary));
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .loading-details {
          font-size: 12px;
          color: rgba(224, 247, 250, 0.6);
          text-align: right;
        }

        /* Binary Animation */
        .binary-animation {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          overflow: hidden;
          z-index: 1;
          pointer-events: none;
        }

        .binary-code {
          position: absolute;
          color: rgba(0, 240, 255, 0.3);
          font-family: 'Courier New', monospace;
          font-size: 12px;
          white-space: nowrap;
          animation: binaryFall linear infinite;
          opacity: 0;
        }

        @keyframes binaryFall {
          0% {
            transform: translateY(-100px);
            opacity: 0;
          }
          10% {
            opacity: 0.7;
          }
          90% {
            opacity: 0.7;
          }
          100% {
            transform: translateY(100vh);
            opacity: 0;
          }
        }

        /* Footer */
        .footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 30px;
          padding-top: 20px;
          border-top: 1px solid rgba(0, 240, 255, 0.1);
        }

        .copyright {
          font-size: 12px;
          color: rgba(224, 247, 250, 0.5);
        }

        .security-badge {
          display: flex;
          align-items: center;
          gap: 6px;
        }

        .security-icon {
          color: #4ade80;
          font-size: 12px;
        }

        .security-text {
          font-size: 11px;
          color: #4ade80;
          font-weight: 500;
        }
      `}</style>
    </div>
  );
};

const SmartLoader: React.FC<SmartLoaderProps> = ({ onLoadingComplete, children }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [isVisible, setIsVisible] = useState(true);

    useEffect(() => {
        // Show loader for exactly 5 seconds
        const timer = setTimeout(() => {
            setIsLoading(false);
            // Start fade out animation
            setTimeout(() => {
                setIsVisible(false);
                onLoadingComplete();
            }, 500); // 500ms for fade out animation
        }, 5000); // 5 seconds

        return () => clearTimeout(timer);
    }, [onLoadingComplete]);

    if (!isVisible) {
        return <>{children}</>;
    }

    return (
        <div className={`smart-loader-overlay ${!isLoading ? 'fade-out' : ''}`} style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            zIndex: 9999,
            opacity: isLoading ? 1 : 0,
            transition: 'opacity 0.5s ease-out'
        }}>
            <DerivLoaderContent />
        </div>
    );
};

export default SmartLoader;
