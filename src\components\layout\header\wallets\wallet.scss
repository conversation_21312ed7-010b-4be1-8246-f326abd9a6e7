@use 'components/shared/styles/constants' as *;
@use 'components/shared/styles/mixins' as *;
@use 'sass:string';
@use 'sass:map';

// As these currencies have the same color, will use this variable for them in the map
$usdt-color: (
    'card': (
        'light': (
            'mobile': radial-gradient(100% 277.78% at 0% 100%, rgb(0 147 147 / 24%) 0%, rgb(0 147 147 / 48%) 100%) #fff,
            'desktop': radial-gradient(100% 277.78% at 0% 100%, rgb(0 147 147 / 24%) 0%, rgb(0 147 147 / 48%) 100%) #fff,
        ),
        'dark': (
            'mobile': radial-gradient(100% 277.78% at 0% 100%, rgb(0 147 147 / 24%) 0%, rgb(0 147 147 / 48%) 100%)
                #151717,
            'desktop': radial-gradient(100% 277.78% at 0% 100%, rgb(0 147 147 / 24%) 0%, rgb(0 147 147 / 48%) 100%)
                #151717,
        ),
    ),
    'header': (
        'light': (
            'mobile':
                radial-gradient(
                    100% 4130.74% at 0% 100%,
                    rgb(0 147 147 / 40%) 0%,
                    rgb(0 147 147 / 16%) 50.52%,
                    rgb(4 217 217 / 40%) 100%
                )
                #fff,
            'desktop':
                radial-gradient(
                    100% 4130.74% at 0% 100%,
                    rgb(0 147 147 / 40%) 0%,
                    rgb(0 147 147 / 16%) 50.52%,
                    rgb(4 217 217 / 40%) 100%
                )
                #fff,
        ),
        'dark': (
            'mobile':
                radial-gradient(
                    100% 4130.74% at 0% 100%,
                    rgb(0 147 147 / 40%) 0%,
                    rgb(0 147 147 / 16%) 50.52%,
                    rgb(4 217 217 / 40%) 100%
                )
                #151717,
            'desktop':
                radial-gradient(
                    100% 4130.74% at 0% 100%,
                    rgb(0 147 147 / 40%) 0%,
                    rgb(0 147 147 / 16%) 50.52%,
                    rgb(4 217 217 / 40%) 100%
                )
                #151717,
        ),
    ),
);
$wallet-bg-color: (
    'usd': (
        'card': (
            'light': (
                'mobile': radial-gradient(100% 4130.74% at 0% 100%, rgb(244 67 54 / 24%) 0%, rgb(40 57 145 / 48%) 100%)
                    #fff,
                'desktop': radial-gradient(100% 4130.74% at 0% 100%, rgb(244 67 54 / 24%) 0%, rgb(40 57 145 / 48%) 100%)
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(244 67 54 / 40%) 0%,
                        rgb(244 67 54 / 16%) 50.52%,
                        rgb(40 57 145 / 56%) 100%
                    )
                    #151717,
                'desktop': radial-gradient(100% 4130.74% at 0% 100%, rgb(244 67 54 / 24%) 0%, rgb(40 57 145 / 48%) 100%)
                    #151717,
            ),
        ),
        'header': (
            'light': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(244 67 54 / 40%) 0%,
                        rgb(244 67 54 / 16%) 50.52%,
                        rgb(40 57 145 / 56%) 100%
                    )
                    #fff,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(244 67 54 / 40%) 0%,
                        rgb(244 67 54 / 16%) 50.52%,
                        rgb(40 57 145 / 56%) 100%
                    )
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(244 67 54 / 40%) 0%,
                        rgb(244 67 54 / 16%) 50.52%,
                        rgb(40 57 145 / 56%) 100%
                    )
                    #151717,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(244 67 54 / 40%) 0%,
                        rgb(244 67 54 / 16%) 50.52%,
                        rgb(40 57 145 / 56%) 100%
                    )
                    #151717,
            ),
        ),
    ),
    'aud': (
        'card': (
            'light': (
                'mobile': radial-gradient(100% 4130.74% at 0% 100%, rgb(13 180 61 / 24%) 0%, rgb(255 205 0 / 48%) 100%)
                    #fff,
                'desktop': radial-gradient(100% 4130.74% at 0% 100%, rgb(13 180 61 / 24%) 0%, rgb(255 205 0 / 48%) 100%)
                    #fff,
            ),
            'dark': (
                'mobile': radial-gradient(100% 4130.74% at 0% 100%, rgb(13 180 61 / 24%) 0%, rgb(255 205 0 / 48%) 100%)
                    #151717,
                'desktop': radial-gradient(100% 4130.74% at 0% 100%, rgb(13 180 61 / 24%) 0%, rgb(255 205 0 / 48%) 100%)
                    #151717,
            ),
        ),
        'header': (
            'light': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(13 180 61 / 40%) 0%,
                        rgb(13 180 61 / 16%) 50.52%,
                        rgb(255 205 0 / 56%) 100%
                    )
                    #fff,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(13 180 61 / 40%) 0%,
                        rgb(13 180 61 / 16%) 50.52%,
                        rgb(255 205 0 / 56%) 100%
                    )
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(13 180 61 / 40%) 0%,
                        rgb(13 180 61 / 16%) 50.52%,
                        rgb(255 205 0 / 56%) 100%
                    )
                    #151717,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(13 180 61 / 40%) 0%,
                        rgb(13 180 61 / 16%) 50.52%,
                        rgb(255 205 0 / 56%) 100%
                    )
                    #151717,
            ),
        ),
    ),
    'eur': (
        'card': (
            'light': (
                'mobile': radial-gradient(100% 4130.74% at 0% 100%, rgb(40 57 145 / 24%) 0%, rgb(248 209 46 / 48%) 100%)
                    #fff,
                'desktop':
                    radial-gradient(100% 4130.74% at 0% 100%, rgb(40 57 145 / 24%) 0%, rgb(248 209 46 / 48%) 100%) #fff,
            ),
            'dark': (
                'mobile': radial-gradient(100% 4130.74% at 0% 100%, rgb(40 57 145 / 24%) 0%, rgb(248 209 46 / 48%) 100%)
                    #151717,
                'desktop':
                    radial-gradient(100% 4130.74% at 0% 100%, rgb(40 57 145 / 24%) 0%, rgb(248 209 46 / 48%) 100%)
                    #151717,
            ),
        ),
        'header': (
            'light': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(40 57 145 / 40%) 0%,
                        rgb(40 57 145 / 16%) 50.52%,
                        rgb(248 209 46 / 56%) 100%
                    )
                    #fff,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(40 57 145 / 40%) 0%,
                        rgb(40 57 145 / 16%) 50.52%,
                        rgb(248 209 46 / 56%) 100%
                    )
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(40 57 145 / 40%) 0%,
                        rgb(40 57 145 / 16%) 50.52%,
                        rgb(248 209 46 / 56%) 100%
                    )
                    #151717,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(40 57 145 / 40%) 0%,
                        rgb(40 57 145 / 16%) 50.52%,
                        rgb(248 209 46 / 56%) 100%
                    )
                    #151717,
            ),
        ),
    ),
    'gbp': (
        'card': (
            'light': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(40 57 145 / 24%) 0%,
                        rgb(40 57 145 / 24%) 0.01%,
                        rgb(244 67 54 / 48%) 100%
                    )
                    #fff,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(40 57 145 / 24%) 0%,
                        rgb(40 57 145 / 24%) 0.01%,
                        rgb(244 67 54 / 48%) 100%
                    )
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(40 57 145 / 24%) 0%,
                        rgb(40 57 145 / 24%) 0.01%,
                        rgb(244 67 54 / 48%) 100%
                    )
                    #151717,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(40 57 145 / 24%) 0%,
                        rgb(40 57 145 / 24%) 0.01%,
                        rgb(244 67 54 / 48%) 100%
                    )
                    #151717,
            ),
        ),
        'header': (
            'light': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(40 57 145 / 40%) 0%,
                        rgb(40 57 145 / 16%) 50.52%,
                        rgb(244 67 54 / 56%) 100%
                    )
                    #fff,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(40 57 145 / 40%) 0%,
                        rgb(40 57 145 / 16%) 50.52%,
                        rgb(244 67 54 / 56%) 100%
                    )
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(40 57 145 / 40%) 0%,
                        rgb(40 57 145 / 16%) 50.52%,
                        rgb(244 67 54 / 56%) 100%
                    )
                    #151717,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(40 57 145 / 40%) 0%,
                        rgb(40 57 145 / 16%) 50.52%,
                        rgb(244 67 54 / 56%) 100%
                    )
                    #151717,
            ),
        ),
    ),
    'p2p': (
        'card': (
            'light': (
                'mobile': radial-gradient(100% 277.78% at 0% 100%, rgb(255 68 79 / 24%) 0%, rgb(255 100 68 / 48%) 100%)
                    #fff,
                'desktop': radial-gradient(100% 277.78% at 0% 100%, rgb(255 68 79 / 24%) 0%, rgb(255 100 68 / 48%) 100%)
                    #fff,
            ),
            'dark': (
                'mobile': radial-gradient(100% 277.78% at 0% 100%, rgb(255 68 79 / 24%) 0%, rgb(255 100 68 / 48%) 100%)
                    #151717,
                'desktop': radial-gradient(100% 277.78% at 0% 100%, rgb(255 68 79 / 24%) 0%, rgb(255 100 68 / 48%) 100%)
                    #151717,
            ),
        ),
        'header': (
            'light': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(255 68 79 / 40%) 0%,
                        rgb(255 68 79 / 16%) 50.52%,
                        rgb(255 100 68 / 56%) 100%
                    )
                    #fff,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(255 68 79 / 40%) 0%,
                        rgb(255 68 79 / 16%) 50.52%,
                        rgb(255 100 68 / 56%) 100%
                    )
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(255 68 79 / 40%) 0%,
                        rgb(255 68 79 / 16%) 50.52%,
                        rgb(255 100 68 / 56%) 100%
                    )
                    #151717,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(255 68 79 / 40%) 0%,
                        rgb(255 68 79 / 16%) 50.52%,
                        rgb(255 100 68 / 56%) 100%
                    )
                    #151717,
            ),
        ),
    ),
    'payment-agent': (
        'card': (
            'light': (
                'mobile':
                    radial-gradient(100% 277.78% at 0% 100%, rgb(151 151 151 / 24%) 0%, rgb(178 194 195 / 48%) 100%)
                    #fff,
                'desktop':
                    radial-gradient(100% 277.78% at 0% 100%, rgb(151 151 151 / 24%) 0%, rgb(178 194 195 / 48%) 100%)
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(100% 277.78% at 0% 100%, rgb(151 151 151 / 24%) 0%, rgb(178 194 195 / 48%) 100%)
                    #151717,
                'desktop':
                    radial-gradient(100% 277.78% at 0% 100%, rgb(151 151 151 / 24%) 0%, rgb(178 194 195 / 48%) 100%)
                    #151717,
            ),
        ),
        'header': (
            'light': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(151 151 151 / 40%) 0%,
                        rgb(151 151 151 / 16%) 50.52%,
                        rgb(178 194 195 / 56%) 100%
                    )
                    #fff,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(151 151 151 / 40%) 0%,
                        rgb(151 151 151 / 16%) 50.52%,
                        rgb(178 194 195 / 56%) 100%
                    )
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(151 151 151 / 40%) 0%,
                        rgb(151 151 151 / 16%) 50.52%,
                        rgb(178 194 195 / 56%) 100%
                    )
                    #151717,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(151 151 151 / 40%) 0%,
                        rgb(151 151 151 / 16%) 50.52%,
                        rgb(178 194 195 / 56%) 100%
                    )
                    #151717,
            ),
        ),
    ),
    'btc': (
        'card': (
            'light': (
                'mobile':
                    radial-gradient(100% 277.78% at 0% 100%, rgb(247 147 27 / 24%) 0%, rgb(247 199 27 / 47.7%) 99.99%)
                    #fff,
                'desktop':
                    radial-gradient(100% 277.78% at 0% 100%, rgb(247 147 27 / 24%) 0%, rgb(247 199 27 / 47.7%) 99.99%)
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(
                        100% 277.78% at 0% 100%,
                        rgb(247 147 27 / 24%) 0%,
                        rgb(247 199 27 / 47.7%) 99.99%,
                        rgb(255 100 68 / 48%) 100%
                    )
                    #151717,
                'desktop':
                    radial-gradient(
                        100% 277.78% at 0% 100%,
                        rgb(247 147 27 / 24%) 0%,
                        rgb(247 199 27 / 47.7%) 99.99%,
                        rgb(255 100 68 / 48%) 100%
                    )
                    #151717,
            ),
        ),
        'header': (
            'light': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(247 147 27 / 40%) 0%,
                        rgb(247 147 27 / 16%) 50.52%,
                        rgb(247 199 27 / 40%) 100%
                    )
                    #fff,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(247 147 27 / 40%) 0%,
                        rgb(247 147 27 / 16%) 50.52%,
                        rgb(247 199 27 / 40%) 100%
                    )
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(247 147 27 / 40%) 0%,
                        rgb(247 147 27 / 16%) 50.52%,
                        rgb(247 199 27 / 40%) 100%
                    )
                    #151717,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(247 147 27 / 40%) 0%,
                        rgb(247 147 27 / 16%) 50.52%,
                        rgb(247 199 27 / 40%) 100%
                    )
                    #151717,
            ),
        ),
    ),
    'eth': (
        'card': (
            'light': (
                'mobile': radial-gradient(100% 277.78% at 0% 100%, rgb(82 86 127 / 24%) 0%, rgb(130 140 173 / 48%) 100%)
                    #fff,
                'desktop':
                    radial-gradient(100% 277.78% at 0% 100%, rgb(82 86 127 / 24%) 0%, rgb(130 140 173 / 48%) 100%) #fff,
            ),
            'dark': (
                'mobile': radial-gradient(100% 277.78% at 0% 100%, rgb(82 86 127 / 24%) 0%, rgb(130 140 173 / 48%) 100%)
                    #151717,
                'desktop':
                    radial-gradient(100% 277.78% at 0% 100%, rgb(82 86 127 / 24%) 0%, rgb(130 140 173 / 48%) 100%)
                    #151717,
            ),
        ),
        'header': (
            'light': (
                'mobile': radial-gradient(100% 277.78% at 0% 100%, rgb(82 86 127 / 24%) 0%, rgb(130 140 173 / 48%) 100%)
                    #fff,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(82 86 127 / 40%) 0%,
                        rgb(82 86 127 / 16%) 50.52%,
                        rgb(130 140 173 / 40%) 100%
                    )
                    #fff,
            ),
            'dark': (
                'mobile': radial-gradient(100% 277.78% at 0% 100%, rgb(82 86 127 / 24%) 0%, rgb(130 140 173 / 48%) 100%)
                    #151717,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(82 86 127 / 40%) 0%,
                        rgb(82 86 127 / 16%) 50.52%,
                        rgb(130 140 173 / 40%) 100%
                    )
                    #151717,
            ),
        ),
    ),
    'ltc': (
        'card': (
            'light': (
                'mobile':
                    radial-gradient(100% 277.78% at 0% 100%, rgb(165 168 169 / 24%) 0%, rgb(193 204 207 / 48%) 100%)
                    #fff,
                'desktop':
                    radial-gradient(100% 277.78% at 0% 100%, rgb(165 168 169 / 24%) 0%, rgb(193 204 207 / 48%) 100%)
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(100% 277.78% at 0% 100%, rgb(165 168 169 / 24%) 0%, rgb(193 204 207 / 48%) 100%)
                    #151717,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(165 168 169 / 40%) 0%,
                        rgb(165 168 169 / 16%) 50.52%,
                        rgb(193 204 207 / 40%) 100%
                    )
                    #151717,
            ),
        ),
        'header': (
            'light': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(165 168 169 / 40%) 0%,
                        rgb(165 168 169 / 16%) 50.52%,
                        rgb(193 204 207 / 40%) 100%
                    )
                    #fff,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(165 168 169 / 40%) 0%,
                        rgb(165 168 169 / 16%) 50.52%,
                        rgb(193 204 207 / 40%) 100%
                    )
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(165 168 169 / 40%) 0%,
                        rgb(165 168 169 / 16%) 50.52%,
                        rgb(193 204 207 / 40%) 100%
                    )
                    #151717,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(165 168 169 / 40%) 0%,
                        rgb(165 168 169 / 16%) 50.52%,
                        rgb(193 204 207 / 40%) 100%
                    )
                    #151717,
            ),
        ),
    ),
    'ust': $usdt-color,
    'usdt': $usdt-color,
    'tusdt': $usdt-color,
    'eusdt': $usdt-color,
    'usdc': (
        'card': (
            'light': (
                'mobile': radial-gradient(100% 277.78% at 0% 100%, rgb(39 117 202 / 24%) 0%, rgb(34 76 225 / 48%) 100%)
                    #fff,
                'desktop': radial-gradient(100% 277.78% at 0% 100%, rgb(39 117 202 / 24%) 0%, rgb(34 76 225 / 48%) 100%)
                    #fff,
            ),
            'dark': (
                'mobile': radial-gradient(100% 277.78% at 0% 100%, rgb(39 117 202 / 24%) 0%, rgb(34 76 225 / 48%) 100%)
                    #151717,
                'desktop': radial-gradient(100% 277.78% at 0% 100%, rgb(39 117 202 / 24%) 0%, rgb(34 76 225 / 48%) 100%)
                    #151717,
            ),
        ),
        'header': (
            'light': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(39 117 202 / 40%) 0%,
                        rgb(39 117 202 / 16%) 50.52%,
                        rgb(34 76 225 / 40%) 100%
                    )
                    #fff,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(39 117 202 / 40%) 0%,
                        rgb(39 117 202 / 16%) 50.52%,
                        rgb(34 76 225 / 40%) 100%
                    )
                    #fff,
            ),
            'dark': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(39 117 202 / 40%) 0%,
                        rgb(39 117 202 / 16%) 50.52%,
                        rgb(34 76 225 / 40%) 100%
                    )
                    #151717,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(39 117 202 / 40%) 0%,
                        rgb(39 117 202 / 16%) 50.52%,
                        rgb(34 76 225 / 40%) 100%
                    )
                    #151717,
            ),
        ),
    ),
    'xrp': (
        'card': (
            'light': (
                'mobile':
                    radial-gradient(295.23% 141.42% at 0% 100%, rgb(108 106 151 / 28%) 0%, rgb(50 48 99 / 48%) 100%)
                    #fff,
                'desktop':
                    radial-gradient(295.23% 141.42% at 0% 100%, rgb(108 106 151 / 28%) 0%, rgb(50 48 99 / 48%) 100%)
                    #fff,
            ),
        ),
        'header': (
            'light': (
                'mobile':
                    radial-gradient(295.23% 141.42% at 0% 100%, rgb(108 106 151 / 28%) 0%, rgb(50 48 99 / 48%) 100%)
                    #fff,
                'desktop':
                    radial-gradient(295.23% 141.42% at 0% 100%, rgb(108 106 151 / 28%) 0%, rgb(50 48 99 / 48%) 100%)
                    #fff,
            ),
        ),
    ),
    'demo': (
        'card': (
            'light': (
                'mobile': radial-gradient(100% 277.78% at 0% 100%, rgb(255 100 68 / 24%) 0%, rgb(255 68 79 / 48%) 100%)
                    #212329,
                'desktop': radial-gradient(100% 277.78% at 0% 100%, rgb(255 100 68 / 24%) 0%, rgb(255 68 79 / 48%) 100%)
                    #212329,
            ),
            'dark': (
                'mobile': radial-gradient(100% 277.78% at 0% 100%, rgb(255 100 68 / 24%) 0%, rgb(255 68 79 / 48%) 100%)
                    #fbdddd,
                'desktop': radial-gradient(100% 277.78% at 0% 100%, rgb(255 100 68 / 24%) 0%, rgb(255 68 79 / 48%) 100%)
                    #fbdddd,
            ),
        ),
        'header': (
            'light': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(255 100 68 / 40%) 0%,
                        rgb(255 100 68 / 16%) 50.52%,
                        rgb(255 68 79 / 40%) 100%
                    )
                    #212329,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(255 100 68 / 40%) 0%,
                        rgb(255 100 68 / 16%) 50.52%,
                        rgb(255 68 79 / 40%) 100%
                    )
                    #212329,
            ),
            'dark': (
                'mobile':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(255 100 68 / 40%) 0%,
                        rgb(255 100 68 / 16%) 50.52%,
                        rgb(255 68 79 / 40%) 100%
                    )
                    #fbdddd,
                'desktop':
                    radial-gradient(
                        100% 4130.74% at 0% 100%,
                        rgb(255 100 68 / 40%) 0%,
                        rgb(255 100 68 / 16%) 50.52%,
                        rgb(255 68 79 / 40%) 100%
                    )
                    #fbdddd,
            ),
        ),
    ),
);

/// Wallet background color generator
///
/// This mixin will generate a helper classes for wallet background color based on the given map
///
/// @example
///     .wallet-header__usd-bg
///     .wallet-header__usd-bg--dark
@mixin wallet-bg-color($currencies-map: $wallet-bg-color) {
    @each $currency, $types in $currencies-map {
        @each $type, $themes in $types {
            @each $theme, $colors in $themes {
                $t: if($theme == 'dark', '--dark', '');

                .wallet-#{$type}__#{string.to-lower-case($currency)}-bg#{$t} {
                    background: map.get($colors, 'desktop');

                    @if $currency == 'demo' {
                        position: relative;

                        &:not([class*='--hide-watermark'], [class*='--small']) {
                            &:before {
                                content: '';
                                display: block;
                                position: absolute;
                                inset: 0;
                                background-repeat: repeat;
                                background-size: 70px;
                                mix-blend-mode: overlay;
                                opacity: 0.24;
                            }
                        }
                    }

                    @include mobile-screen {
                        background: map.get($colors, 'mobile');
                    }
                }
            }
        }
    }
}

// Init mixins
@include wallet-bg-color;
