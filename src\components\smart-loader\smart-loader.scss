.smart-loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #1e1e5e 0%, #161650 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease-out;

    &.fade-out {
        opacity: 0;
    }
}

.smart-loader-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.smart-loader-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* Default loader styles (you can replace this with your custom loader) */
.default-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;

    h2 {
        font-size: 3.2rem;
        font-weight: 700;
        margin: 0;
        color: #ffffff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    p {
        font-size: 1.6rem;
        margin: 0;
        opacity: 0.8;
        color: #ffffff;
    }
}

.loader-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .default-loader {
        h2 {
            font-size: 2.4rem;
        }

        p {
            font-size: 1.4rem;
        }
    }

    .loader-spinner {
        width: 50px;
        height: 50px;
    }
}

/* 
INSTRUCTIONS FOR CUSTOM LOADER:
1. Replace the .default-loader section with your custom loader styles
2. Replace the content inside .smart-loader-content with your loader HTML
3. Make sure your loader is responsive and works on all screen sizes
4. Keep the .smart-loader-overlay and .smart-loader-container styles as they handle the timing and transitions
*/
