@use 'components/shared/styles/constants' as *;
@use 'components/shared/styles/mixins' as *;

/**
 * @define journal
 */
@keyframes move-in {
    0% {
        transform: translateX(300px);
        opacity: 0;
    }

    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.journal {
    .ReactVirtualized__Grid__innerScrollContainer {
        & > :first-child,
        :before,
        :after {
            box-sizing: border-box;
            animation: move-in 0.4s ease-in forwards;
        }
    }

    &-empty {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;

        svg {
            g {
                path {
                    fill: var(--text-less-prominent);
                }
            }
        }

        &__header {
            padding: 0.8rem;
        }

        &__icon {
            align-self: center;
        }

        &__message {
            margin: 0 auto;

            & .dc-text {
                line-height: var(--text-lh-xxl);
            }
        }

        &__list {
            list-style-type: disc;
            margin-left: 20px;
            line-height: var(--text-lh-xl);

            li::marker {
                color: var(--text-less-prominent);
            }
        }
    }

    &__item {
        padding: 16px;

        &-list {
            height: calc(100% - 7.2rem);
        }

        &-content {
            > * {
                width: 100%;
            }
        }
    }

    &__data-list {
        .ReactVirtualized__Grid__innerScrollContainer {
            > div:nth-child(even) {
                background: var(--general-section-2);
            }
        }
    }

    &-tools {
        &__container {
            display: flex;
            justify-content: space-between;
            padding: 12px;
            padding-left: 16px;
            border: solid 1px var(--general-section-1);

            &-filter {
                @include flex-center(flex-end);

                cursor: pointer;
                flex: 1;

                &--label {
                    margin-inline-end: 0.8rem;
                }

                .deriv-text {
                    font-weight: lighter;
                }
            }
        }
    }

    &__text {
        font-size: var(--text-size-xxs);
        line-height: 1.5;
        display: inline;
        color: var(--text-general);

        &-time,
        &-date {
            display: inline;
        }

        &-datetime {
            color: var(--text-less-prominent);
            font-size: var(--text-size--xxxs);
            margin-top: 6px;
        }

        &--error {
            color: var(--status-danger);
        }

        &--warn {
            color: var(--status-warning);
        }

        &--info {
            color: var(--status-info);
        }

        &--success {
            color: var(--status-success);
        }

        &--bold {
            font-weight: bold;
        }
    }

    &__loader {
        width: 350px;
        height: 9.2rem;

        &--mobile {
            @extend .journal__loader;

            width: 100vw;
        }
    }
}

.filter-dialog {
    position: fixed;
    display: grid;
    grid-gap: 1.6rem;
    background: var(--general-main-2);
    border-radius: $BORDER_RADIUS;
    box-shadow: 0 4px 16px 0 var(--shadow-menu);
    transition:
        transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1),
        opacity 0.25s linear;
    padding: 1.6rem 0.8rem;
    padding-inline-end: 3.6rem;
    inset-inline-end: 16px;
    z-index: 99;

    &--enter-done {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }

    &--enter,
    &--exit {
        opacity: 0;
        transform: translate3d(0, -20px, 0);
    }

    &__input {
        .input-wrapper__input {
            border: 1px solid var(--border-normal);
        }
    }

    &__button {
        margin-top: 0.8rem;

        .dc-btn {
            width: 100%;
        }
    }
}
