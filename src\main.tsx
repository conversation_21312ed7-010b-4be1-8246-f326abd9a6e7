import React, { useState } from 'react';
import ReactDOM from 'react-dom/client';
import { AuthWrapper } from './app/AuthWrapper';
import { AnalyticsInitializer } from './utils/analytics';
import SmartLoader from './components/smart-loader';
import './styles/index.scss';

AnalyticsInitializer();

const AppWithLoader: React.FC = () => {
    const [showApp, setShowApp] = useState(false);

    const handleLoadingComplete = () => {
        setShowApp(true);
    };

    return (
        <SmartLoader onLoadingComplete={handleLoadingComplete}>
            {showApp && <AuthWrapper />}
        </SmartLoader>
    );
};

ReactDOM.createRoot(document.getElementById('root')!).render(<AppWithLoader />);
