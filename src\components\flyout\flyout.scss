@use 'components/shared/styles/constants' as *;
@use 'components/shared/styles/mixins' as *;

@keyframes fade-in {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

.flyout {
    $flyout: &;
    $default-margin: 15px;
    $button-padding: 5px 20px;

    position: absolute;
    inset-inline-start: 250px;
    top: 0;
    background-color: var(--general-main-2);
    height: calc(100vh - 232px);
    max-height: calc(100vh - 232px);
    z-index: 11;
    border-radius: $BORDER_RADIUS;
    font-size: 2em;
    margin-inline-start: $default-margin;
    margin-top: 20px;
    box-shadow: 0 2px 8px 0 var(--shadow-box);
    min-width: 400px;
    visibility: hidden;

    &__item:hover {
        .flyout__button-add--hide {
            display: flex !important;
            animation: fade-in 0.3s;
        }
    }

    &__content {
        overflow: auto;
        height: calc(100% - 64px);

        .dc-themed-scrollbars {
            padding: 5px 25px;
        }

        &-disclaimer {
            display: flex;
            justify-content: space-around;
            background: $color-yellow;
            font-size: var(--text-size-xs);
            margin-top: 1.6em;
            line-height: 1.3em;
            padding: 0.8rem;
            border-radius: 4px;

            &-text {
                color: $color-black-1;
                width: 324px;
            }

            &-icon {
                padding-top: 0.8rem;
            }
        }
    }

    &__block-workspace {
        &--top {
            margin-bottom: $default-margin;
        }

        &--center {
            margin-top: 0.6em;

            .injectionDiv {
                height: 100%;
            }
        }

        &__header {
            display: flex;
        }
    }

    &__button {
        &-new {
            width: 20%;
            height: 4rem !important;
            font-size: var(--text-size-xs);
            font-weight: bold;
            border-top-left-radius: 0 !important;
            border-bottom-left-radius: 0 !important;
        }

        &-add {
            color: var(--general-main-1);

            &--hide {
                display: none !important;
            }
        }

        &-back {
            padding: 0 15px;
            align-self: center;
            background-color: transparent;
            color: $COLOR_BLACK;

            svg {
                vertical-align: middle;

                @include is-RTL {
                    transform: rotate(180deg);
                }
            }

            &:focus {
                outline: none;
            }
        }

        &-next,
        &-previous {
            margin-inline-start: 1em;
            color: $COLOR_LIGHT_BLACK_1;
            background-color: var(--general-section-1);
            display: flex;
        }
    }

    &__item {
        line-height: 1.3em;
        font-size: var(--text-size-xs);

        &:not(:last-of-type) {
            margin-bottom: 30px;
        }

        &-header {
            display: flex;
            margin-top: $default-margin;
            margin-bottom: 10px;
        }

        &-buttons {
            margin-inline-start: auto;
            align-self: center;
        }

        &-info {
            cursor: pointer;
            font-weight: bold;
            display: block;
            color: $COLOR_RED;
        }

        &-description {
            font-size: var(--text-size-xs);
            margin-bottom: 1em;
            line-height: 1.3em;
            color: var(--text-general);
        }
    }

    &__image {
        width: 100%;
        height: auto;
        border-radius: 0.5em;
    }

    &__video {
        width: 100%;
        height: 20vh;
        border-radius: 0.5em;
    }

    &__help {
        padding: 0;
        height: 100%;
        visibility: visible;

        &-header {
            padding: 15px;
            display: flex;
            background-color: var(--general-section-1);
        }

        &-content {
            padding: 1.5em;
            font-size: 0.8em;
            overflow-y: auto;
            height: calc(100vh - 295px);

            #{$flyout}__item {
                margin-bottom: 0.8em;
            }
        }

        &-title {
            align-self: center;
        }

        &-footer {
            display: flex;
            justify-content: flex-end;
            padding: 0.5em 0.8em;
            border-top: solid 0.1em var(--general-section-1);
        }
    }

    &__search {
        padding: 0;
        visibility: visible;

        &-header {
            padding: 20px;
            background-color: var(--general-disabled);
            display: flex;
            justify-content: space-between;

            &-text {
                align-self: center;
            }
        }

        &-empty {
            padding: 25px 0;
        }
        #{$flyout}__help-content {
            height: calc(100% - 60px);
        }
    }

    &__normal {
        visibility: visible;

        &-content {
            height: 100%;
        }
    }

    &__input {
        width: 80% !important;
        height: 4rem;
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        border: solid 1px $color-grey-5 !important;
        display: inline-block !important;
        margin-top: 3.3rem;
    }

    &__hr {
        height: 2px;
        width: 100%;
        border-top: 1px solid var(--general-section-1);
        position: absolute;
        left: 0;
        right: 0;
        margin: 0;
    }
}
